#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

#
# Commit message length validation
# Enforces maximum 50 character commit messages
#

commit_file="$1"
commit_message=$(cat "$commit_file")

# Remove any leading/trailing whitespace
commit_message=$(echo "$commit_message" | sed 's/^[[:space:]]*//;s/[[:space:]]*$//')

# Check if commit message is empty
if [ -z "$commit_message" ]; then
    echo "❌ Commit message cannot be empty!"
    exit 1
fi

# Check if commit message is too long (max 50 characters)
message_length=$(echo "$commit_message" | wc -c)
if [ "$message_length" -gt 50 ]; then
    echo "❌ Commit message is too long!"
    echo "📏 Current length: $message_length characters (max: 50)"
    echo "📝 Your message: '$commit_message'"
    echo ""
    echo "✅ Commit messages should be:"
    echo "   • Maximum 50 characters"
    echo "   • Clear and concise"
    echo "   • Use format: 'type: brief description'"
    echo ""
    echo "Examples of good commit messages:"
    echo "   • feat: add user authentication"
    echo "   • fix: resolve login bug"
    echo "   • docs: update README"
    echo "   • refactor: simplify user service"
    echo ""
    exit 1
fi

echo "✅ Commit message looks good! ($message_length chars)"
exit 0
